package model

import (
	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func computeChargeForExposure(charge *ptypes.Charge, exposure *ptypes.Exposure) (*decimal.Decimal, error) {
	amount, err := charge.Calculate(exposure)
	if err != nil {
		return nil, err
	}
	return pointer_utils.ToPointer(amount), nil
}

// CalculateBaseChargeDistributionAtSubCovLevel calculates the charge distribution at the sub-coverage level. It returns the
// total charges at the sub cov level and the "Base" charges for each sub coverage
func CalculateBaseChargeDistributionAtSubCovLevel(
	charges []*ptypes.Charge,
	exposure *ptypes.Exposure,
) (*decimal.Decimal, map[ptypes.SubCoverageType]decimal.Decimal, error) {
	totalCharges := decimal.Zero
	chargesBySubCov := make(map[ptypes.SubCoverageType]decimal.Decimal)
	for _, c := range charges {
		baseChargeClass := c.GetBaseCharge()
		if baseChargeClass == nil {
			continue
		}
		chargeableSubCovGroup := c.GetChargedSubCoverageGroup()
		if chargeableSubCovGroup == nil {
			continue
		}
		subCovGroup := chargeableSubCovGroup.GetGroup()
		subCovs := subCovGroup.GetSubCoverages()
		if len(subCovs) != 1 {
			return nil, nil, errors.Newf("expected exactly one sub-coverage in the group, got %+v", subCovs)
		}
		charge, err := computeChargeForExposure(c, exposure)
		if err != nil {
			return nil, nil, err
		}
		totalCharges = totalCharges.Add(*charge)
		subCov := subCovs[0]
		subCovCharge, found := chargesBySubCov[subCov]
		if found {
			subCovCharge = subCovCharge.Add(*charge)
		} else {
			subCovCharge = *charge
		}
		chargesBySubCov[subCov] = subCovCharge
	}
	return pointer_utils.ToPointer(totalCharges), chargesBySubCov, nil
}

func CalculateChargeDistributionAtVehicleLevel(charges []*ptypes.Charge, exposure *ptypes.Exposure) (*decimal.Decimal, map[string]map[ptypes.SubCoverageType]decimal.Decimal, error) {
	totalCharges := decimal.Zero
	chargesByVehicle := make(map[string]map[ptypes.SubCoverageType]decimal.Decimal)
	for _, c := range charges {
		distributions := c.GetDistributions()
		if len(distributions) == 0 {
			continue
		}
		chargeableSubCovGroup := c.GetChargedSubCoverageGroup()
		if chargeableSubCovGroup == nil {
			continue
		}
		subCovGroup := chargeableSubCovGroup.GetGroup()
		subCovs := subCovGroup.GetSubCoverages()
		if len(subCovs) != 1 {

		}
		charge, err := computeChargeForExposure(c, exposure)
		if err != nil {
			return nil, nil, err
		}
		totalCharges = totalCharges.Add(*charge)
		subCov := subCovs[0]

		// Process each distribution to calculate vehicle-level charges
		for _, distribution := range distributions {
			if distribution.GetType() != ptypes.Charge_DistributionType_Vehicle {
				continue
			}
			for _, item := range distribution.GetItems() {
				vehicleID := item.GetId()
				fractionStr := item.GetFraction()

				// Parse fraction string to decimal
				fraction, err := decimal.NewFromString(fractionStr)
				if err != nil {
					return nil, nil, errors.Wrapf(err, "failed to parse fraction %s for vehicle %s", fractionStr, vehicleID)
				}

				// Calculate vehicle charge based on fraction
				vehicleCharge := charge.Mul(fraction)

				// Initialize vehicle map if it doesn't exist
				if chargesByVehicle[vehicleID] == nil {
					chargesByVehicle[vehicleID] = make(map[ptypes.SubCoverageType]decimal.Decimal)
				}

				// Add charge to existing amount for this vehicle and sub-coverage
				existingCharge, found := chargesByVehicle[vehicleID][subCov]
				if found {
					chargesByVehicle[vehicleID][subCov] = existingCharge.Add(vehicleCharge)
				} else {
					chargesByVehicle[vehicleID][subCov] = vehicleCharge
				}
			}
		}
	}
	return pointer_utils.ToPointer(totalCharges), chargesByVehicle, nil
}

// CalculateFeeChargeAndSurchargeDistribution calculates the fee charge and surcharge distribution for the charges.
func CalculateFeeChargeAndSurchargeDistribution(
	charges []*ptypes.Charge,
	exposure *ptypes.Exposure,
) (*PolicyCharge, error) {
	totalCharges := decimal.Zero
	var surcharge, feeCharge decimal.Decimal
	for _, c := range charges {
		charge, err := computeChargeForExposure(c, exposure)
		if err != nil {
			return nil, err
		}
		if c.IsFeeCharge() {
			feeCharge = feeCharge.Add(*charge)
			totalCharges = totalCharges.Add(*charge)
		} else if c.IsSurcharge() {
			surcharge = surcharge.Add(*charge)
			totalCharges = totalCharges.Add(*charge)
		}
	}
	return &PolicyCharge{
		TotalCharge: totalCharges,
		Surcharge:   surcharge,
		FeeCharge:   feeCharge,
	}, nil
}

type PolicyCharge struct {
	TotalCharge decimal.Decimal
	Surcharge   decimal.Decimal
	FeeCharge   decimal.Decimal
}

type ChargeDistribution struct {
	TotalCharge *decimal.Decimal
	// ChargesBySubCoverage is a map that holds the charges for each sub-coverage.
	// It does not include the surcharges/fee-charges per sub-coverage.
	// They are included in the Surcharges/FeeCharges fields.
	ChargesBySubCoverage map[ptypes.SubCoverageType]decimal.Decimal
	Surcharges           decimal.Decimal
	// FeeCharges is the total flat fee charges.
	FeeCharges decimal.Decimal
}

// FeeBreakdown is a struct that holds the fees for an endorsement broken down by coverage.
// It has three fields: PreEndorsement, PostEndorsement, and Endorsement.
// PreEndorsement is the fees before the endorsement, PostEndorsement is the fees after the endorsement,
// and Endorsement is the fees for the endorsement.
type FeeBreakdown struct {
	PreEndorsement  map[appEnums.Coverage]decimal.Decimal
	PostEndorsement map[appEnums.Coverage]decimal.Decimal
	Endorsement     map[appEnums.Coverage]decimal.Decimal
}

package impl

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	"nirvanatech.com/nirvana/insurance-bundle/model/charges"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"strconv"
	"time"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/durationpb"

	"nirvanatech.com/nirvana/business-auto/enums"
	ba_jobs "nirvanatech.com/nirvana/business-auto/jobs"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/type_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	insuranceBundleModel "nirvanatech.com/nirvana/insurance-bundle/model"
	insurancecore_coverage "nirvanatech.com/nirvana/insurance-core/coverage"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/pdfgen"
	policyutils "nirvanatech.com/nirvana/policy/business_auto"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
)

const (
	GenerateQuotePDFTaskID    = "GenerateBusinessAutoQuotePDFTask"
	pdfTemplate               = "quoteBizAuto"
	QuotePDFExpirationDefault = time.Minute * 15
)

func NewGenerateQuotePDFJob(deps *Deps) (*jtypes.Job[*ba_jobs.GenerateQuotePDFArgs], error) {
	return jtypes.NewJob(
		ba_jobs.GenerateQuotePDF,
		[]jtypes.TaskCreator[*ba_jobs.GenerateQuotePDFArgs]{
			func() jtypes.Task[*ba_jobs.GenerateQuotePDFArgs] {
				return &GenerateQuotePDFTask{deps: deps}
			},
		},
		ba_jobs.GenerateQuotePDFUnmarshalFn,
	)
}

type GenerateQuotePDFTask struct {
	job_utils.NonRetryableTask[*ba_jobs.GenerateQuotePDFArgs]
	job_utils.NoopUndoTask[*ba_jobs.GenerateQuotePDFArgs]
	deps *Deps
}

func (t *GenerateQuotePDFTask) ID() string {
	return GenerateQuotePDFTaskID
}

func (t *GenerateQuotePDFTask) Run(
	jCtx jtypes.Context,
	msg *ba_jobs.GenerateQuotePDFArgs,
) error {
	err := validateGenerateQuotePDFArgs(msg)
	if err != nil {
		log.Error(jCtx, "failed to validate GenerateQuotePDFArgs", log.Err(err))
		return errors.Wrap(err, "failed to validate GenerateQuotePDFArgs for business auto")
	}

	log.Info(jCtx, "GenerateQuotePDF job started",
		log.Stringer("applicationID", msg.ApplicationID),
		log.Stringer("signaturePacketID", msg.SignaturePacketID),
		log.String("applicationType", msg.ApplicationType.String()))

	jCtx = jCtx.WithUpdatedBaseCtx(func(ctx context.Context) context.Context {
		return log.ContextWithFields(ctx, log.Stringer("applicationID", msg.ApplicationID))
	})

	// Get signature packet compilation if provided
	var sigPacketComp *compilation.FormsCompilation
	if msg.SignaturePacketID != uuid.Nil {
		sigPacketComp, err = t.deps.FormsWrapper.GetFormCompilationById(jCtx, msg.SignaturePacketID)
		if err != nil {
			log.Error(jCtx, "failed to get signature packet compilation", log.Err(err))
			return errors.Wrapf(err, "failed to get signature packet compilation for ID %s", msg.SignaturePacketID)
		}
	}

	// Create quote PDF inputs (with actual forms and customer data)
	quotePDFInputs, err := t.createBusinessAutoQuotePDFInputs(jCtx, msg.ApplicationID, sigPacketComp)
	if err != nil {
		log.Error(jCtx, "failed to create quote PDF inputs", log.Err(err))
		return errors.Wrap(err, "failed to create quote PDF inputs")
	}

	// Marshal inputs to JSON
	data, err := json.Marshal(quotePDFInputs)
	if err != nil {
		log.Error(jCtx, "failed to marshal quote PDF inputs for business auto", log.Err(err))
		return errors.Wrap(err, "failed to marshal quote PDF inputs for business auto")
	}

	// Generate filename
	fileName := generateFileNameForQuotePDF(
		t.deps.Clock,
		quotePDFInputs.CustomerInfo.Name,
		quotePDFInputs.CustomerInfo.FeinNumber,
	)

	// Call PDF generation service
	resp, err := t.deps.PDFGenClient.Generate(jCtx, &pdfgen.GeneratePDFRequest{
		UseReactPdf: true,
		Template:    pdfTemplate,
		Data:        data,
		LinkExpiry:  durationpb.New(QuotePDFExpirationDefault),
		FileName:    fileName,
	})
	if err != nil {
		log.Error(jCtx, "failed to generate business auto quote PDF", log.Err(err))
		return errors.Wrap(err, "failed to generate business auto quote PDF")
	}

	// Parse handle ID from response
	handleID, err := uuid.Parse(resp.HandleUuid)
	if err != nil {
		log.Error(jCtx, "failed to parse handle UUID from PDF generation response", log.Err(err))
		return errors.Wrap(err, "failed to parse handle UUID from PDF generation response")
	}

	log.Info(jCtx, "Successfully generated business auto quote PDF",
		log.String("fileName", fileName),
		log.Stringer("handleID", handleID),
		log.String("downloadUrl", resp.DownloadLink))

	// Update business auto application with quote PDF handle ID
	err = t.deps.BusinessAutoAppWrapper.UpdateApp(jCtx, msg.ApplicationID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		if app.DocumentsInfo == nil {
			app.DocumentsInfo = &model.DocumentsInfo{}
		}
		app.DocumentsInfo.QuotePDFHandleID = &handleID
		return app, nil
	})
	if err != nil {
		log.Error(jCtx, "failed to update business auto application with quote PDF handle ID", log.Err(err))
		return errors.Wrap(err, "failed to update business auto application with quote PDF handle ID")
	}

	log.Info(jCtx, "GenerateQuotePDF job completed successfully")
	return nil
}

func (t *GenerateQuotePDFTask) createBusinessAutoQuotePDFInputs(jCtx jtypes.Context, applicationID uuid.UUID, sigPacketComp *compilation.FormsCompilation) (*businessAutoQuotePDFInputs, error) {
	app, err := t.deps.BusinessAutoAppWrapper.GetByID(jCtx, applicationID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get business auto application")
	}

	var forms []*form
	if sigPacketComp != nil {
		formsList := (*sigPacketComp).FlattenAllForms().ToSlice()
		for _, formItem := range formsList {
			forms = append(forms, &form{
				Code: formItem.Code,
				Name: formItem.Name,
			})
		}
	}

	var agencyName, producerName string
	if agencyInfo, err := t.deps.AgencyWrapper.FetchAgency(jCtx, app.AgencyID); err == nil {
		agencyName = agencyInfo.Name
	}
	if producerInfo, err := t.deps.AuthWrapper.FetchUserInfo(jCtx, app.ProducerID); err == nil {
		producerName = producerInfo.FirstName + " " + producerInfo.LastName
	}

	// Generate policy number using business auto policy utilities
	policyNumberImpl, err := policyutils.GeneratePolicyNumber(
		app_enums.CoverageAutoLiability,
		app.EffectiveDurationStart,
		string(app.ShortID),
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate policy number for application %s", app.ID.String())
	}
	policyNumber := policyNumberImpl.String()

	dotNumber := type_utils.GetValueOrDefault(app.CompanyInfo.DOTNumber, 0)
	dotNumberStr := fmt.Sprintf("%d", dotNumber)
	if dotNumber == 0 {
		dotNumberStr = "-"
	}

	// Extract actual customer info from application
	customerInfo := &customerInfo{
		Name: app.CompanyInfo.Name,
		PolicyPeriod: &policyPeriod{
			StartDate: app.EffectiveDurationStart.Format("Jan 2, 2006"),
			EndDate:   app.EffectiveDurationEnd.Format("Jan 2, 2006"),
		},
		DotNumber:    dotNumberStr,
		FeinNumber:   type_utils.GetValueOrDefault(app.CompanyInfo.FEIN, "-"),
		Agency:       agencyName,
		Producer:     producerName,
		PolicyNumber: policyNumber,
	}

	// Get pricing context using the selected pricing context ID from the application
	var pricingContext *quoting.PricingContext
	if app.SelectedQuotingPricingContextID != nil {
		pricingContext, err = t.deps.PricingWrapper.GetQuotingPricingContextById(jCtx,
			app.SelectedQuotingPricingContextID.String())
		if err != nil {
			log.Error(jCtx, "failed to get quoting pricing context",
				log.String("pricingContextID", app.SelectedQuotingPricingContextID.String()),
				log.Err(err))
			return nil, errors.Wrapf(err, "failed to get pricing context for application %s", app.ID.String())
		}
	} else {
		log.Warn(jCtx, "no pricing context ID found for application", log.String("appID", app.ID.String()))
	}
	chargeList := &charges.ChargeList{}
	if pricingContext != nil && pricingContext.Charges != nil && pricingContext.Charges.PolicyCharges != nil {
		chargeList = pricingContext.Charges.PolicyCharges[policyNumber]
	}

	totalCharge, chargesBySubCovForSegment, err := insuranceBundleModel.CalculateBaseChargeDistributionAtSubCovLevel(
		chargeList.GetCharges(), ptypes.NewNilExposure())
	if err != nil {
		return nil, err
	}

	limitForAllSubCoverages := app.CoveragesInfo.GetLimitForAllSubCoverages()

	i := 0
	var coverages []*coverage
	for subCov, premium := range chargesBySubCovForSegment {
		subCoverage, err := insurancecore_coverage.GetAppCoverageFromPricingSubCoverage(subCov)
		if err != nil {
			return nil, err
		}
		coverages = append(coverages, &coverage{
			Symbol:      strconv.Itoa(i + 1),
			DisplayName: subCov.String(),
			Limit:       limitForAllSubCoverages[*subCoverage],
			Premium:     premium.String(),
		})
		i++
	}

	//_, vehicleLevelPremiums, err := insuranceBundleModel.CalculateChargeDistributionAtVehicleLevel(chargeList.GetCharges(), ptypes.NewNilExposure())
	//if err != nil {
	//	return nil, err
	//}

	//for _, charge := range chargeList.Charges {
	//	subCovGroup, ok := charge.ChargedItem.(*ptypes.Charge_ChargedSubCoverageGroup)
	//	charge.GetChargedItem()
	//	if !ok {
	//		continue
	//	}
	//	for _, subCov := range subCovGroup.ChargedSubCoverageGroup.GetGroup().GetSubCoverages() {
	//}

	// Create vehicles using actual vehicle data from application
	var vehicles []*vehicle
	if app.VehiclesInfo != nil {
		for _, v := range *app.VehiclesInfo {
			//vehicleLevelPremiums[v.VIN][ptypes.SubCoverageType(app_enums.CoverageAutoLiability)]
			vehicles = append(vehicles, &vehicle{
				Year:   int(v.Year),
				Make:   v.Make,
				Model:  v.Model,
				Vin:    v.VIN,
				Radius: convertRadiusClassificationToDisplayText(v.RadiusClassification),
				// TODO: Remove hardcoded values
				LiabilityPremium:  "3865",
				UmPremium:         "Incl.",
				UimPremium:        "Incl.",
				MedPayPipPremium:  "N/A",
				PhysDamagePremium: "336",
				UnitSubTotal:      "4601",
				CollComp:          "$25,000",
				Deductible:        "1000",
			})
		}
	}

	// Keep the rest as mock data for now
	return &businessAutoQuotePDFInputs{
		QuoteNumber:  policyNumber,
		CustomerInfo: customerInfo,
		Coverages:    coverages,
		Vehicles:     vehicles,
		PricingInfo: &pricingInfo{
			TotalInsValue:   "675000",
			Assessments:     "145",
			TotalPremium:    totalCharge.String(),
			PremiumSubTotal: "136872",
			TexasAntiTheft:  "145",
		},
		Forms: forms,
	}, nil
}

func generateFileNameForQuotePDF(clk clock.Clock, insuredName, feinNumber string) string {
	timestamp := clk.Now().Format("2006-01-02")
	return insuredName + " - Business Auto Quote " + feinNumber + " - " + timestamp + ".pdf"
}

func validateGenerateQuotePDFArgs(args *ba_jobs.GenerateQuotePDFArgs) error {
	if args == nil {
		return errors.New("nil args")
	}
	if args.ApplicationID == uuid.Nil {
		return errors.New("nil applicationID")
	}
	return nil
}

func convertRadiusClassificationToDisplayText(radius enums.RadiusClassification) string {
	switch radius {
	case enums.RadiusClassification0To100:
		return "Up to 100 Miles"
	case enums.RadiusClassification101To300:
		return "101-300 Miles"
	case enums.RadiusClassificationGreaterThan301:
		return "Over 300 Miles"
	default:
		return "" // default fallback
	}
}

var _ jtypes.Task[*ba_jobs.GenerateQuotePDFArgs] = (*GenerateQuotePDFTask)(nil)
